# 独立验证服务 Dockerfile
FROM python:3.10-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖（最小化）
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 复制验证服务专用的requirements
COPY requirements.verify.txt .

# 安装Python依赖（最小化）
RUN pip install --no-cache-dir -r requirements.verify.txt

# 只复制验证服务需要的文件
COPY verify_server.py .
COPY utils/data_manager.py ./utils/
COPY config/verify_config.py ./config/
COPY templates/verify.html ./templates/
COPY static/ ./static/

# 创建必要的目录
RUN mkdir -p processed data logs

# 设置环境变量
ENV FLASK_ENV=production
ENV PYTHONPATH=/app
ENV SERVICE_TYPE=verify

# 暴露端口
EXPOSE 5005

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:5005/health || exit 1

# 创建非root用户
RUN useradd -m -u 1000 appuser && chown -R appuser:appuser /app
USER appuser

# 启动命令
CMD ["gunicorn", "--bind", "0.0.0.0:5005", "--workers", "2", "--timeout", "120", "--access-logfile", "/app/logs/access.log", "--error-logfile", "/app/logs/error.log", "verify_server:app"]
