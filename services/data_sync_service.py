#!/usr/bin/env python3
"""
数据同步服务 - 负责数据备份和同步
确保验证服务能够独立访问必要的数据
"""

import os
import time
import json
import shutil
import logging
import threading
from datetime import datetime, timedelta
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/app/logs/datasync.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('DataSyncService')

class DataSyncService:
    """数据同步服务"""
    
    def __init__(self):
        self.data_dir = Path('/app/data')
        self.processed_dir = Path('/app/processed')
        self.backup_dir = Path('/app/backup')
        self.backup_interval = int(os.environ.get('BACKUP_INTERVAL', 300))  # 默认5分钟
        
        # 确保目录存在
        self.backup_dir.mkdir(exist_ok=True)
        
        self.running = True
        
    def create_backup(self):
        """创建数据备份"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_path = self.backup_dir / f'backup_{timestamp}'
            backup_path.mkdir(exist_ok=True)
            
            # 备份映射数据
            mapping_file = self.data_dir / 'url_mapping.json'
            if mapping_file.exists():
                shutil.copy2(mapping_file, backup_path / 'url_mapping.json')
                logger.info(f"备份映射数据到: {backup_path}")
            
            # 备份处理后的文件（增量备份）
            processed_backup = backup_path / 'processed'
            processed_backup.mkdir(exist_ok=True)
            
            # 只备份最近24小时的文件
            cutoff_time = datetime.now() - timedelta(hours=24)
            
            for file_path in self.processed_dir.glob('*'):
                if file_path.is_file():
                    file_mtime = datetime.fromtimestamp(file_path.stat().st_mtime)
                    if file_mtime > cutoff_time:
                        shutil.copy2(file_path, processed_backup / file_path.name)
            
            logger.info(f"备份完成: {backup_path}")
            
            # 清理旧备份（保留最近7天）
            self.cleanup_old_backups()
            
        except Exception as e:
            logger.error(f"备份失败: {str(e)}")
    
    def cleanup_old_backups(self):
        """清理旧备份"""
        try:
            cutoff_time = datetime.now() - timedelta(days=7)
            
            for backup_path in self.backup_dir.glob('backup_*'):
                if backup_path.is_dir():
                    backup_time_str = backup_path.name.replace('backup_', '')
                    try:
                        backup_time = datetime.strptime(backup_time_str, '%Y%m%d_%H%M%S')
                        if backup_time < cutoff_time:
                            shutil.rmtree(backup_path)
                            logger.info(f"删除旧备份: {backup_path}")
                    except ValueError:
                        # 忽略格式不正确的目录
                        pass
                        
        except Exception as e:
            logger.error(f"清理旧备份失败: {str(e)}")
    
    def verify_data_integrity(self):
        """验证数据完整性"""
        try:
            mapping_file = self.data_dir / 'url_mapping.json'
            
            if not mapping_file.exists():
                logger.warning("映射文件不存在")
                return False
            
            # 验证JSON格式
            with open(mapping_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 验证文件存在性
            missing_files = []
            for verification_id, info in data.items():
                processed_filename = info.get('processed_filename')
                if processed_filename:
                    file_path = self.processed_dir / processed_filename
                    if not file_path.exists():
                        missing_files.append(processed_filename)
            
            if missing_files:
                logger.warning(f"发现缺失文件: {missing_files}")
                return False
            
            logger.info("数据完整性验证通过")
            return True
            
        except Exception as e:
            logger.error(f"数据完整性验证失败: {str(e)}")
            return False
    
    def get_service_stats(self):
        """获取服务统计信息"""
        try:
            mapping_file = self.data_dir / 'url_mapping.json'
            
            if not mapping_file.exists():
                return {
                    'total_mappings': 0,
                    'total_files': 0,
                    'backup_count': 0,
                    'last_backup': None
                }
            
            with open(mapping_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 统计备份数量
            backup_count = len(list(self.backup_dir.glob('backup_*')))
            
            # 获取最新备份时间
            latest_backup = None
            for backup_path in self.backup_dir.glob('backup_*'):
                if backup_path.is_dir():
                    backup_time_str = backup_path.name.replace('backup_', '')
                    try:
                        backup_time = datetime.strptime(backup_time_str, '%Y%m%d_%H%M%S')
                        if latest_backup is None or backup_time > latest_backup:
                            latest_backup = backup_time
                    except ValueError:
                        pass
            
            return {
                'total_mappings': len(data),
                'total_files': len(list(self.processed_dir.glob('*'))),
                'backup_count': backup_count,
                'last_backup': latest_backup.isoformat() if latest_backup else None,
                'data_integrity': self.verify_data_integrity()
            }
            
        except Exception as e:
            logger.error(f"获取统计信息失败: {str(e)}")
            return {'error': str(e)}
    
    def run_backup_loop(self):
        """运行备份循环"""
        logger.info(f"启动数据同步服务，备份间隔: {self.backup_interval}秒")
        
        while self.running:
            try:
                # 创建备份
                self.create_backup()
                
                # 验证数据完整性
                self.verify_data_integrity()
                
                # 等待下次备份
                time.sleep(self.backup_interval)
                
            except KeyboardInterrupt:
                logger.info("收到停止信号")
                self.running = False
                break
            except Exception as e:
                logger.error(f"备份循环异常: {str(e)}")
                time.sleep(60)  # 出错后等待1分钟再重试
    
    def stop(self):
        """停止服务"""
        self.running = False

def main():
    """主函数"""
    service = DataSyncService()
    
    try:
        # 启动备份循环
        backup_thread = threading.Thread(target=service.run_backup_loop)
        backup_thread.daemon = True
        backup_thread.start()
        
        # 保持主线程运行
        while True:
            time.sleep(60)
            stats = service.get_service_stats()
            logger.info(f"服务状态: {stats}")
            
    except KeyboardInterrupt:
        logger.info("正在停止数据同步服务...")
        service.stop()
    except Exception as e:
        logger.error(f"服务异常: {str(e)}")
    finally:
        logger.info("数据同步服务已停止")

if __name__ == '__main__':
    main()
