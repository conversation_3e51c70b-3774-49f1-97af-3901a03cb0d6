#!/usr/bin/env python3
"""
监控服务 - 负责系统监控和自动恢复
"""

import os
import time
import json
import logging
import requests
import subprocess
import threading
from datetime import datetime, timedelta
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/app/logs/monitoring.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('MonitoringService')

class ServiceMonitor:
    """服务监控器"""
    
    def __init__(self):
        self.services = {
            'main-app': {
                'url': 'http://main-app:6065/health',
                'container': 'pdf-qr-main',
                'critical': False
            },
            'verify-service-1': {
                'url': 'http://verify-service-1:5005/health',
                'container': 'pdf-qr-verify-1',
                'critical': True
            },
            'verify-service-2': {
                'url': 'http://verify-service-2:5005/health',
                'container': 'pdf-qr-verify-2',
                'critical': True
            },
            'data-sync': {
                'url': None,  # 数据同步服务没有HTTP接口
                'container': 'pdf-qr-datasync',
                'critical': True
            }
        }
        
        self.check_interval = 30  # 30秒检查一次
        self.failure_threshold = 3  # 连续失败3次触发恢复
        self.failure_counts = {name: 0 for name in self.services.keys()}
        self.running = True
        
    def check_service_health(self, service_name, service_config):
        """检查单个服务健康状态"""
        try:
            if service_config['url']:
                # HTTP健康检查
                response = requests.get(service_config['url'], timeout=10)
                if response.status_code == 200:
                    return True, "健康"
                else:
                    return False, f"HTTP状态码: {response.status_code}"
            else:
                # 容器状态检查
                result = subprocess.run(
                    ['docker', 'inspect', '--format={{.State.Running}}', service_config['container']],
                    capture_output=True, text=True, timeout=10
                )
                if result.returncode == 0 and result.stdout.strip() == 'true':
                    return True, "容器运行中"
                else:
                    return False, "容器未运行"
                    
        except requests.RequestException as e:
            return False, f"请求异常: {str(e)}"
        except subprocess.TimeoutExpired:
            return False, "检查超时"
        except Exception as e:
            return False, f"检查异常: {str(e)}"
    
    def restart_service(self, service_name, service_config):
        """重启服务"""
        try:
            logger.warning(f"正在重启服务: {service_name}")
            
            # 重启容器
            result = subprocess.run(
                ['docker', 'restart', service_config['container']],
                capture_output=True, text=True, timeout=60
            )
            
            if result.returncode == 0:
                logger.info(f"服务重启成功: {service_name}")
                self.failure_counts[service_name] = 0
                return True
            else:
                logger.error(f"服务重启失败: {service_name}, 错误: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"重启服务异常: {service_name}, 错误: {str(e)}")
            return False
    
    def send_alert(self, service_name, message):
        """发送告警（可扩展为邮件、短信等）"""
        alert_data = {
            'timestamp': datetime.now().isoformat(),
            'service': service_name,
            'message': message,
            'severity': 'critical' if self.services[service_name]['critical'] else 'warning'
        }
        
        # 记录到日志
        logger.error(f"告警: {alert_data}")
        
        # 这里可以扩展为发送邮件、短信等
        # send_email_alert(alert_data)
        # send_sms_alert(alert_data)
    
    def monitor_loop(self):
        """监控循环"""
        logger.info("启动服务监控")
        
        while self.running:
            try:
                for service_name, service_config in self.services.items():
                    is_healthy, status_message = self.check_service_health(service_name, service_config)
                    
                    if is_healthy:
                        if self.failure_counts[service_name] > 0:
                            logger.info(f"服务恢复正常: {service_name}")
                            self.failure_counts[service_name] = 0
                    else:
                        self.failure_counts[service_name] += 1
                        logger.warning(f"服务健康检查失败: {service_name}, 状态: {status_message}, 失败次数: {self.failure_counts[service_name]}")
                        
                        # 达到失败阈值，尝试重启
                        if self.failure_counts[service_name] >= self.failure_threshold:
                            self.send_alert(service_name, f"服务连续失败{self.failure_threshold}次，正在尝试重启")
                            
                            if not self.restart_service(service_name, service_config):
                                self.send_alert(service_name, "服务重启失败，需要人工干预")
                
                time.sleep(self.check_interval)
                
            except KeyboardInterrupt:
                logger.info("收到停止信号")
                self.running = False
                break
            except Exception as e:
                logger.error(f"监控循环异常: {str(e)}")
                time.sleep(60)
    
    def get_service_status(self):
        """获取所有服务状态"""
        status = {}
        for service_name, service_config in self.services.items():
            is_healthy, status_message = self.check_service_health(service_name, service_config)
            status[service_name] = {
                'healthy': is_healthy,
                'status': status_message,
                'failure_count': self.failure_counts[service_name],
                'critical': service_config['critical']
            }
        return status
    
    def stop(self):
        """停止监控"""
        self.running = False

class PrometheusManager:
    """Prometheus管理器"""
    
    def __init__(self):
        self.prometheus_process = None
        
    def start_prometheus(self):
        """启动Prometheus"""
        try:
            logger.info("启动Prometheus")
            self.prometheus_process = subprocess.Popen([
                '/usr/local/bin/prometheus',
                '--config.file=/app/monitoring/prometheus.yml',
                '--storage.tsdb.path=/app/prometheus',
                '--web.console.libraries=/usr/local/share/prometheus/console_libraries',
                '--web.console.templates=/usr/local/share/prometheus/consoles',
                '--web.enable-lifecycle'
            ])
            logger.info("Prometheus启动成功")
        except Exception as e:
            logger.error(f"Prometheus启动失败: {str(e)}")
    
    def stop_prometheus(self):
        """停止Prometheus"""
        if self.prometheus_process:
            self.prometheus_process.terminate()
            self.prometheus_process.wait()

class GrafanaManager:
    """Grafana管理器"""
    
    def __init__(self):
        self.grafana_process = None
        
    def start_grafana(self):
        """启动Grafana"""
        try:
            logger.info("启动Grafana")
            env = os.environ.copy()
            env['GF_PATHS_DATA'] = '/app/grafana'
            env['GF_PATHS_LOGS'] = '/app/logs'
            
            self.grafana_process = subprocess.Popen([
                '/opt/grafana/bin/grafana-server',
                '--config=/app/monitoring/grafana.ini'
            ], env=env)
            logger.info("Grafana启动成功")
        except Exception as e:
            logger.error(f"Grafana启动失败: {str(e)}")
    
    def stop_grafana(self):
        """停止Grafana"""
        if self.grafana_process:
            self.grafana_process.terminate()
            self.grafana_process.wait()

def main():
    """主函数"""
    # 初始化组件
    monitor = ServiceMonitor()
    prometheus_manager = PrometheusManager()
    grafana_manager = GrafanaManager()
    
    try:
        # 启动Prometheus和Grafana
        prometheus_manager.start_prometheus()
        time.sleep(10)  # 等待Prometheus启动
        grafana_manager.start_grafana()
        time.sleep(10)  # 等待Grafana启动
        
        # 启动监控循环
        monitor_thread = threading.Thread(target=monitor.monitor_loop)
        monitor_thread.daemon = True
        monitor_thread.start()
        
        # 保持主线程运行
        while True:
            time.sleep(60)
            status = monitor.get_service_status()
            logger.info(f"服务状态摘要: {json.dumps(status, indent=2, ensure_ascii=False)}")
            
    except KeyboardInterrupt:
        logger.info("正在停止监控服务...")
        monitor.stop()
        prometheus_manager.stop_prometheus()
        grafana_manager.stop_grafana()
    except Exception as e:
        logger.error(f"监控服务异常: {str(e)}")
    finally:
        logger.info("监控服务已停止")

if __name__ == '__main__':
    main()
