#!/bin/bash

# 高可用性部署脚本
# 用于部署独立的验证服务架构

set -e

echo "🚀 开始部署高可用性PDF二维码系统..."

# 检查Docker和Docker Compose
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，请先安装Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose未安装，请先安装Docker Compose"
    exit 1
fi

# 创建必要的目录结构
echo "📁 创建目录结构..."
mkdir -p {uploads,processed,data,backup,logs/{main,verify-1,verify-2,datasync,monitoring,nginx}}
mkdir -p {monitoring,nginx/ssl}

# 设置目录权限
chmod 755 uploads processed data backup logs
chmod -R 755 logs/

# 停止现有服务
echo "🛑 停止现有服务..."
docker-compose -f docker-compose.yml down 2>/dev/null || true
docker-compose -f docker-compose.ha.yml down 2>/dev/null || true

# 备份现有数据
if [ -f "data/url_mapping.json" ]; then
    echo "💾 备份现有数据..."
    cp data/url_mapping.json data/url_mapping.json.backup.$(date +%Y%m%d_%H%M%S)
fi

# 构建和启动高可用性服务
echo "🔨 构建高可用性服务..."
docker-compose -f docker-compose.ha.yml build

echo "🚀 启动高可用性服务..."
docker-compose -f docker-compose.ha.yml up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 30

# 检查服务状态
echo "🔍 检查服务状态..."

services=("pdf-qr-main" "pdf-qr-verify-1" "pdf-qr-verify-2" "pdf-qr-datasync" "pdf-qr-nginx")
all_healthy=true

for service in "${services[@]}"; do
    if docker ps --filter "name=$service" --filter "status=running" | grep -q "$service"; then
        echo "✅ $service: 运行中"
    else
        echo "❌ $service: 未运行"
        all_healthy=false
    fi
done

# 健康检查
echo "🏥 执行健康检查..."

# 检查主应用
for i in {1..30}; do
    if curl -f http://localhost:6065/health > /dev/null 2>&1; then
        echo "✅ 主应用健康检查通过"
        break
    fi
    if [ $i -eq 30 ]; then
        echo "❌ 主应用健康检查失败"
        all_healthy=false
    fi
    echo "⏳ 等待主应用启动... ($i/30)"
    sleep 2
done

# 检查验证服务1
for i in {1..30}; do
    if curl -f http://localhost:5005/health > /dev/null 2>&1; then
        echo "✅ 验证服务1健康检查通过"
        break
    fi
    if [ $i -eq 30 ]; then
        echo "❌ 验证服务1健康检查失败"
        all_healthy=false
    fi
    echo "⏳ 等待验证服务1启动... ($i/30)"
    sleep 2
done

# 检查验证服务2
for i in {1..30}; do
    if curl -f http://localhost:5006/health > /dev/null 2>&1; then
        echo "✅ 验证服务2健康检查通过"
        break
    fi
    if [ $i -eq 30 ]; then
        echo "❌ 验证服务2健康检查失败"
        all_healthy=false
    fi
    echo "⏳ 等待验证服务2启动... ($i/30)"
    sleep 2
done

# 检查Nginx负载均衡器
for i in {1..30}; do
    if curl -f http://localhost:80/health > /dev/null 2>&1; then
        echo "✅ Nginx负载均衡器健康检查通过"
        break
    fi
    if [ $i -eq 30 ]; then
        echo "❌ Nginx负载均衡器健康检查失败"
        all_healthy=false
    fi
    echo "⏳ 等待Nginx启动... ($i/30)"
    sleep 2
done

# 输出部署结果
echo ""
if [ "$all_healthy" = true ]; then
    echo "🎉 高可用性部署成功！"
    echo ""
    echo "📊 服务访问地址："
    echo "  主应用: http://localhost:6065 (内部)"
    echo "  验证服务1: http://localhost:5005 (内部)"
    echo "  验证服务2: http://localhost:5006 (内部)"
    echo "  负载均衡器: http://localhost:80 (外部访问)"
    echo "  监控面板: http://localhost:9090 (Prometheus)"
    echo "  监控仪表板: http://localhost:3000 (Grafana)"
    echo ""
    echo "🔧 管理命令："
    echo "  查看服务状态: docker-compose -f docker-compose.ha.yml ps"
    echo "  查看日志: docker-compose -f docker-compose.ha.yml logs -f [service_name]"
    echo "  停止服务: docker-compose -f docker-compose.ha.yml down"
    echo "  重启服务: docker-compose -f docker-compose.ha.yml restart [service_name]"
    echo ""
    echo "📁 数据目录："
    echo "  上传文件: ./uploads"
    echo "  处理文件: ./processed"
    echo "  数据文件: ./data"
    echo "  备份文件: ./backup"
    echo "  日志文件: ./logs"
    echo ""
    echo "🧪 运行故障转移测试："
    echo "  python3 scripts/failover_test.py"
    echo ""
else
    echo "❌ 部署过程中发现问题，请检查日志："
    echo "  docker-compose -f docker-compose.ha.yml logs"
    exit 1
fi

# 可选：运行基本测试
read -p "是否运行基本功能测试？(y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🧪 运行基本功能测试..."
    
    # 创建测试验证ID
    test_id="test$(date +%s)"
    echo "创建测试验证ID: $test_id"
    
    # 这里可以添加更多测试逻辑
    echo "✅ 基本功能测试完成"
fi

echo ""
echo "🎯 下一步："
echo "1. 配置SSL证书到 nginx/ssl/ 目录"
echo "2. 更新DNS解析指向负载均衡器"
echo "3. 运行完整的故障转移测试"
echo "4. 配置监控告警"
