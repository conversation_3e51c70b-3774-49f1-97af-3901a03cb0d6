#!/usr/bin/env python3
"""
验证服务独立性测试脚本
专门测试验证服务在主应用服务崩溃时的独立运行能力
"""

import os
import time
import json
import requests
import subprocess
import logging
from datetime import datetime
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('IndependenceTest')

class IndependenceTest:
    """验证服务独立性测试"""
    
    def __init__(self):
        self.verify_url = 'http://localhost:5005'
        self.main_app_url = 'http://localhost:6065'
        self.nginx_url = 'http://localhost:80'
        
        # 创建测试数据
        self.test_data = self.create_test_data()
        
    def create_test_data(self):
        """创建测试数据"""
        test_id = f"test_{int(time.time())}"
        
        # 创建测试映射数据
        test_mapping = {
            test_id: {
                'original_filename': 'test_document.pdf',
                'processed_filename': f'final_test_document_{test_id}.png',
                'file_type': 'pdf',
                'created_at': datetime.now().isoformat(),
                'access_count': 0,
                'last_accessed': None
            }
        }
        
        # 创建测试文件
        test_file_path = Path('processed') / test_mapping[test_id]['processed_filename']
        test_file_path.parent.mkdir(exist_ok=True)
        
        # 创建一个简单的测试图片文件
        try:
            from PIL import Image
            img = Image.new('RGB', (100, 100), color='white')
            img.save(test_file_path, 'PNG')
            logger.info(f"创建测试文件: {test_file_path}")
        except ImportError:
            # 如果没有PIL，创建一个空文件
            test_file_path.touch()
            logger.info(f"创建空测试文件: {test_file_path}")
        
        # 更新映射文件
        mapping_file = Path('data/url_mapping.json')
        mapping_file.parent.mkdir(exist_ok=True)
        
        if mapping_file.exists():
            with open(mapping_file, 'r', encoding='utf-8') as f:
                existing_data = json.load(f)
        else:
            existing_data = {}
        
        existing_data.update(test_mapping)
        
        with open(mapping_file, 'w', encoding='utf-8') as f:
            json.dump(existing_data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"创建测试验证ID: {test_id}")
        return test_id
    
    def check_service_status(self, url, service_name):
        """检查服务状态"""
        try:
            response = requests.get(f"{url}/health", timeout=5)
            status = response.status_code == 200
            logger.info(f"{service_name}: {'运行中' if status else '不可用'}")
            return status
        except Exception as e:
            logger.info(f"{service_name}: 不可用 ({str(e)})")
            return False
    
    def test_verification_access(self, base_url, verification_id):
        """测试验证访问"""
        try:
            response = requests.get(f"{base_url}/{verification_id}", timeout=10)
            success = response.status_code in [200, 404]  # 200=文件存在, 404=文件不存在但服务正常
            
            return {
                'success': success,
                'status_code': response.status_code,
                'response_time': response.elapsed.total_seconds(),
                'content_length': len(response.content) if success else 0
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'response_time': None
            }
    
    def stop_main_app(self):
        """停止主应用服务"""
        try:
            logger.info("停止主应用服务...")
            result = subprocess.run(
                ['docker', 'stop', 'pdf-qr-main'],
                capture_output=True, text=True, timeout=30
            )
            success = result.returncode == 0
            if success:
                logger.info("✅ 主应用服务已停止")
            else:
                logger.error(f"❌ 停止主应用服务失败: {result.stderr}")
            return success
        except Exception as e:
            logger.error(f"❌ 停止主应用服务异常: {str(e)}")
            return False
    
    def start_main_app(self):
        """启动主应用服务"""
        try:
            logger.info("启动主应用服务...")
            result = subprocess.run(
                ['docker', 'start', 'pdf-qr-main'],
                capture_output=True, text=True, timeout=30
            )
            success = result.returncode == 0
            if success:
                logger.info("✅ 主应用服务已启动")
                # 等待服务完全启动
                time.sleep(30)
            else:
                logger.error(f"❌ 启动主应用服务失败: {result.stderr}")
            return success
        except Exception as e:
            logger.error(f"❌ 启动主应用服务异常: {str(e)}")
            return False
    
    def run_continuous_test(self, duration_seconds=300):
        """运行持续测试"""
        logger.info(f"开始持续验证测试，持续时间: {duration_seconds}秒")
        
        start_time = time.time()
        test_results = []
        
        while time.time() - start_time < duration_seconds:
            # 测试直接访问验证服务
            direct_result = self.test_verification_access(self.verify_url, self.test_data)
            
            # 测试通过负载均衡器访问
            lb_result = self.test_verification_access(self.nginx_url, self.test_data)
            
            test_results.append({
                'timestamp': datetime.now().isoformat(),
                'direct_access': direct_result,
                'load_balancer_access': lb_result
            })
            
            # 记录结果
            if direct_result['success'] and lb_result['success']:
                status = "✅"
            elif direct_result['success'] or lb_result['success']:
                status = "⚠️"
            else:
                status = "❌"
            
            logger.info(f"{status} 直接访问: {direct_result['success']}, 负载均衡: {lb_result['success']}")
            
            time.sleep(2)  # 每2秒测试一次
        
        return test_results
    
    def analyze_results(self, test_results):
        """分析测试结果"""
        total_tests = len(test_results)
        
        direct_success = sum(1 for r in test_results if r['direct_access']['success'])
        lb_success = sum(1 for r in test_results if r['load_balancer_access']['success'])
        
        direct_success_rate = (direct_success / total_tests * 100) if total_tests > 0 else 0
        lb_success_rate = (lb_success / total_tests * 100) if total_tests > 0 else 0
        
        # 计算平均响应时间
        direct_response_times = [r['direct_access']['response_time'] for r in test_results 
                               if r['direct_access'].get('response_time')]
        lb_response_times = [r['load_balancer_access']['response_time'] for r in test_results 
                           if r['load_balancer_access'].get('response_time')]
        
        avg_direct_time = sum(direct_response_times) / len(direct_response_times) if direct_response_times else 0
        avg_lb_time = sum(lb_response_times) / len(lb_response_times) if lb_response_times else 0
        
        return {
            'total_tests': total_tests,
            'direct_success_count': direct_success,
            'direct_success_rate': direct_success_rate,
            'lb_success_count': lb_success,
            'lb_success_rate': lb_success_rate,
            'avg_direct_response_time': avg_direct_time,
            'avg_lb_response_time': avg_lb_time
        }
    
    def run_independence_test(self):
        """运行完整的独立性测试"""
        logger.info("🧪 开始验证服务独立性测试")
        
        # 1. 检查初始状态
        logger.info("1️⃣ 检查初始服务状态")
        main_app_running = self.check_service_status(self.main_app_url, "主应用服务")
        verify_running = self.check_service_status(self.verify_url, "验证服务")
        
        if not verify_running:
            logger.error("❌ 验证服务未运行，无法进行测试")
            return False
        
        # 2. 基线测试
        logger.info("2️⃣ 执行基线测试")
        baseline_result = self.test_verification_access(self.verify_url, self.test_data)
        logger.info(f"基线测试结果: {baseline_result}")
        
        if not baseline_result['success']:
            logger.error("❌ 基线测试失败，检查测试数据")
            return False
        
        # 3. 停止主应用服务
        logger.info("3️⃣ 停止主应用服务")
        if not self.stop_main_app():
            logger.error("❌ 无法停止主应用服务")
            return False
        
        # 4. 验证主应用确实停止
        logger.info("4️⃣ 验证主应用服务状态")
        time.sleep(10)
        main_app_stopped = not self.check_service_status(self.main_app_url, "主应用服务")
        verify_still_running = self.check_service_status(self.verify_url, "验证服务")
        
        if not main_app_stopped:
            logger.warning("⚠️ 主应用服务可能未完全停止")
        
        if not verify_still_running:
            logger.error("❌ 验证服务在主应用停止后也停止了，说明存在依赖关系")
            self.start_main_app()  # 尝试恢复
            return False
        
        # 5. 持续测试验证服务独立性
        logger.info("5️⃣ 持续测试验证服务独立性 (5分钟)")
        test_results = self.run_continuous_test(300)  # 5分钟测试
        
        # 6. 恢复主应用服务
        logger.info("6️⃣ 恢复主应用服务")
        self.start_main_app()
        
        # 7. 分析结果
        logger.info("7️⃣ 分析测试结果")
        analysis = self.analyze_results(test_results)
        
        # 输出详细结果
        logger.info("📊 测试结果分析:")
        logger.info(f"  总测试次数: {analysis['total_tests']}")
        logger.info(f"  直接访问成功率: {analysis['direct_success_rate']:.2f}%")
        logger.info(f"  负载均衡访问成功率: {analysis['lb_success_rate']:.2f}%")
        logger.info(f"  平均直接访问响应时间: {analysis['avg_direct_response_time']:.3f}秒")
        logger.info(f"  平均负载均衡响应时间: {analysis['avg_lb_response_time']:.3f}秒")
        
        # 判断测试是否通过
        # 标准：直接访问成功率 >= 98%，负载均衡访问成功率 >= 95%
        direct_passed = analysis['direct_success_rate'] >= 98.0
        lb_passed = analysis['lb_success_rate'] >= 95.0
        
        overall_passed = direct_passed and lb_passed
        
        logger.info("🎯 测试结论:")
        logger.info(f"  直接访问测试: {'✅ 通过' if direct_passed else '❌ 失败'}")
        logger.info(f"  负载均衡测试: {'✅ 通过' if lb_passed else '❌ 失败'}")
        logger.info(f"  总体独立性测试: {'✅ 通过' if overall_passed else '❌ 失败'}")
        
        if overall_passed:
            logger.info("🎉 验证服务独立性测试通过！验证服务可以在主应用服务崩溃时独立运行。")
        else:
            logger.error("💥 验证服务独立性测试失败！验证服务可能依赖主应用服务。")
        
        return overall_passed
    
    def cleanup(self):
        """清理测试数据"""
        try:
            # 删除测试文件
            test_file_path = Path('processed') / f'final_test_document_{self.test_data}.png'
            if test_file_path.exists():
                test_file_path.unlink()
                logger.info(f"删除测试文件: {test_file_path}")
            
            # 从映射文件中删除测试数据
            mapping_file = Path('data/url_mapping.json')
            if mapping_file.exists():
                with open(mapping_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                if self.test_data in data:
                    del data[self.test_data]
                    
                    with open(mapping_file, 'w', encoding='utf-8') as f:
                        json.dump(data, f, ensure_ascii=False, indent=2)
                    
                    logger.info(f"从映射文件中删除测试数据: {self.test_data}")
        
        except Exception as e:
            logger.warning(f"清理测试数据时出错: {str(e)}")

def main():
    """主函数"""
    test = IndependenceTest()
    
    try:
        success = test.run_independence_test()
        exit_code = 0 if success else 1
        
    except KeyboardInterrupt:
        logger.info("测试被用户中断")
        exit_code = 1
    except Exception as e:
        logger.error(f"测试异常: {str(e)}")
        exit_code = 1
    finally:
        test.cleanup()
    
    exit(exit_code)

if __name__ == '__main__':
    main()
