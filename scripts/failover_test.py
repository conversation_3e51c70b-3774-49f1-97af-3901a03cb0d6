#!/usr/bin/env python3
"""
故障转移测试脚本
用于测试验证服务的高可用性和故障转移能力
"""

import os
import time
import requests
import subprocess
import threading
import logging
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('FailoverTest')

class FailoverTester:
    """故障转移测试器"""
    
    def __init__(self):
        self.verify_urls = [
            'http://localhost:5005',  # 主验证服务
            'http://localhost:5006',  # 备用验证服务
        ]
        self.nginx_url = 'http://localhost:80'
        self.test_verification_id = 'test123456'  # 需要预先创建的测试ID
        
        self.test_results = []
        self.running = True
        
    def check_service_availability(self, url, timeout=5):
        """检查服务可用性"""
        try:
            response = requests.get(f"{url}/health", timeout=timeout)
            return response.status_code == 200
        except Exception as e:
            logger.debug(f"服务检查失败 {url}: {str(e)}")
            return False
    
    def test_verification_access(self, base_url, verification_id):
        """测试验证访问"""
        try:
            response = requests.get(f"{base_url}/{verification_id}", timeout=10)
            return {
                'success': response.status_code in [200, 404],  # 404也是正常响应
                'status_code': response.status_code,
                'response_time': response.elapsed.total_seconds()
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'response_time': None
            }
    
    def stop_container(self, container_name):
        """停止容器"""
        try:
            result = subprocess.run(
                ['docker', 'stop', container_name],
                capture_output=True, text=True, timeout=30
            )
            return result.returncode == 0
        except Exception as e:
            logger.error(f"停止容器失败 {container_name}: {str(e)}")
            return False
    
    def start_container(self, container_name):
        """启动容器"""
        try:
            result = subprocess.run(
                ['docker', 'start', container_name],
                capture_output=True, text=True, timeout=30
            )
            return result.returncode == 0
        except Exception as e:
            logger.error(f"启动容器失败 {container_name}: {str(e)}")
            return False
    
    def continuous_access_test(self, duration_seconds=300):
        """持续访问测试"""
        logger.info(f"开始持续访问测试，持续时间: {duration_seconds}秒")
        
        start_time = time.time()
        success_count = 0
        failure_count = 0
        
        while time.time() - start_time < duration_seconds and self.running:
            result = self.test_verification_access(self.nginx_url, self.test_verification_id)
            
            if result['success']:
                success_count += 1
            else:
                failure_count += 1
                logger.warning(f"访问失败: {result}")
            
            time.sleep(1)  # 每秒访问一次
        
        total_requests = success_count + failure_count
        success_rate = (success_count / total_requests * 100) if total_requests > 0 else 0
        
        logger.info(f"持续访问测试完成:")
        logger.info(f"  总请求数: {total_requests}")
        logger.info(f"  成功数: {success_count}")
        logger.info(f"  失败数: {failure_count}")
        logger.info(f"  成功率: {success_rate:.2f}%")
        
        return {
            'total_requests': total_requests,
            'success_count': success_count,
            'failure_count': failure_count,
            'success_rate': success_rate
        }
    
    def test_primary_service_failure(self):
        """测试主服务故障场景"""
        logger.info("=== 测试主验证服务故障场景 ===")
        
        # 1. 检查初始状态
        logger.info("1. 检查初始服务状态")
        for i, url in enumerate(self.verify_urls):
            available = self.check_service_availability(url)
            logger.info(f"  验证服务 {i+1}: {'可用' if available else '不可用'}")
        
        # 2. 停止主验证服务
        logger.info("2. 停止主验证服务")
        if self.stop_container('pdf-qr-verify-1'):
            logger.info("  主验证服务已停止")
        else:
            logger.error("  停止主验证服务失败")
            return False
        
        # 3. 等待故障检测
        logger.info("3. 等待故障检测和切换 (30秒)")
        time.sleep(30)
        
        # 4. 测试服务可用性
        logger.info("4. 测试故障转移后的服务可用性")
        test_results = []
        for i in range(10):
            result = self.test_verification_access(self.nginx_url, self.test_verification_id)
            test_results.append(result)
            logger.info(f"  测试 {i+1}: {'成功' if result['success'] else '失败'}")
            time.sleep(2)
        
        success_count = sum(1 for r in test_results if r['success'])
        logger.info(f"  故障转移测试成功率: {success_count/len(test_results)*100:.1f}%")
        
        # 5. 恢复主验证服务
        logger.info("5. 恢复主验证服务")
        if self.start_container('pdf-qr-verify-1'):
            logger.info("  主验证服务已恢复")
            time.sleep(30)  # 等待服务完全启动
        else:
            logger.error("  恢复主验证服务失败")
        
        return success_count >= 8  # 80%以上成功率认为测试通过
    
    def test_main_app_failure(self):
        """测试主应用服务故障对验证服务的影响"""
        logger.info("=== 测试主应用服务故障对验证服务的影响 ===")
        
        # 1. 记录基线性能
        logger.info("1. 记录基线性能")
        baseline_result = self.test_verification_access(self.nginx_url, self.test_verification_id)
        logger.info(f"  基线响应时间: {baseline_result.get('response_time', 'N/A')}秒")
        
        # 2. 停止主应用服务
        logger.info("2. 停止主应用服务")
        if self.stop_container('pdf-qr-main'):
            logger.info("  主应用服务已停止")
        else:
            logger.error("  停止主应用服务失败")
            return False
        
        # 3. 持续测试验证服务
        logger.info("3. 测试验证服务独立性 (60秒)")
        
        def test_worker():
            return self.continuous_access_test(60)
        
        # 在后台运行持续测试
        with ThreadPoolExecutor(max_workers=1) as executor:
            future = executor.submit(test_worker)
            
            # 等待测试完成
            test_result = future.result()
        
        # 4. 恢复主应用服务
        logger.info("4. 恢复主应用服务")
        if self.start_container('pdf-qr-main'):
            logger.info("  主应用服务已恢复")
        else:
            logger.error("  恢复主应用服务失败")
        
        # 验证服务独立性测试通过标准：成功率 >= 95%
        independence_test_passed = test_result['success_rate'] >= 95.0
        
        logger.info(f"验证服务独立性测试: {'通过' if independence_test_passed else '失败'}")
        logger.info(f"  成功率: {test_result['success_rate']:.2f}%")
        
        return independence_test_passed
    
    def test_load_balancing(self):
        """测试负载均衡"""
        logger.info("=== 测试负载均衡 ===")
        
        # 并发请求测试
        def make_request():
            return self.test_verification_access(self.nginx_url, self.test_verification_id)
        
        logger.info("发送100个并发请求测试负载均衡")
        
        with ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(make_request) for _ in range(100)]
            results = [future.result() for future in futures]
        
        success_count = sum(1 for r in results if r['success'])
        avg_response_time = sum(r['response_time'] for r in results if r['response_time']) / len([r for r in results if r['response_time']])
        
        logger.info(f"负载均衡测试结果:")
        logger.info(f"  成功率: {success_count/len(results)*100:.1f}%")
        logger.info(f"  平均响应时间: {avg_response_time:.3f}秒")
        
        return success_count >= 95  # 95%以上成功率
    
    def run_all_tests(self):
        """运行所有测试"""
        logger.info("开始故障转移和高可用性测试")
        
        test_results = {}
        
        try:
            # 测试1: 主验证服务故障
            test_results['primary_failure'] = self.test_primary_service_failure()
            
            # 测试2: 主应用服务故障对验证服务的影响
            test_results['main_app_failure'] = self.test_main_app_failure()
            
            # 测试3: 负载均衡
            test_results['load_balancing'] = self.test_load_balancing()
            
        except KeyboardInterrupt:
            logger.info("测试被用户中断")
            self.running = False
        
        # 输出测试总结
        logger.info("=== 测试总结 ===")
        for test_name, result in test_results.items():
            status = "通过" if result else "失败"
            logger.info(f"{test_name}: {status}")
        
        all_passed = all(test_results.values())
        logger.info(f"总体测试结果: {'全部通过' if all_passed else '存在失败'}")
        
        return test_results

def main():
    """主函数"""
    tester = FailoverTester()
    
    try:
        results = tester.run_all_tests()
        
        # 返回适当的退出码
        exit_code = 0 if all(results.values()) else 1
        exit(exit_code)
        
    except Exception as e:
        logger.error(f"测试异常: {str(e)}")
        exit(1)

if __name__ == '__main__':
    main()
