# 主应用服务 Dockerfile
FROM python:3.10-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    poppler-utils \
    libpoppler-cpp-dev \
    pkg-config \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 复制主应用的requirements
COPY requirements.main.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.main.txt

# 复制主应用需要的文件
COPY app.py .
COPY utils/ ./utils/
COPY config/main_config.py ./config/
COPY templates/ ./templates/
COPY static/ ./static/

# 创建必要的目录
RUN mkdir -p uploads processed data logs

# 设置环境变量
ENV FLASK_ENV=production
ENV PYTHONPATH=/app
ENV SERVICE_TYPE=main

# 暴露端口
EXPOSE 6065

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:6065/health || exit 1

# 创建非root用户
RUN useradd -m -u 1000 appuser && chown -R appuser:appuser /app
USER appuser

# 启动命令
CMD ["gunicorn", "--bind", "0.0.0.0:6065", "--workers", "2", "--timeout", "120", "--access-logfile", "/app/logs/access.log", "--error-logfile", "/app/logs/error.log", "app:app"]
