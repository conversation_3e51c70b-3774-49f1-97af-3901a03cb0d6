version: '3.8'

# 高可用性架构 - 独立服务部署
services:
  # 主应用服务
  main-app:
    build: 
      context: .
      dockerfile: Dockerfile.main
    container_name: pdf-qr-main
    restart: unless-stopped
    ports:
      - "6065:6065"
    volumes:
      # 主应用专用目录
      - ./uploads:/app/uploads
      - ./processed:/app/processed
      - ./data:/app/data
      - ./logs/main:/app/logs
    environment:
      - FLASK_ENV=production
      - PYTHONPATH=/app
      - SERVICE_TYPE=main
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6065/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - pdf-qr-network
    depends_on:
      - data-sync
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # 验证服务 - 主实例
  verify-service-1:
    build:
      context: .
      dockerfile: Dockerfile.verify
    container_name: pdf-qr-verify-1
    restart: unless-stopped
    ports:
      - "5005:5005"
    volumes:
      # 只读访问处理后的文件
      - ./processed:/app/processed:ro
      - ./data:/app/data:ro
      - ./logs/verify-1:/app/logs
    environment:
      - FLASK_ENV=production
      - PYTHONPATH=/app
      - SERVICE_TYPE=verify
      - INSTANCE_ID=verify-1
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5005/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - pdf-qr-network
    depends_on:
      - data-sync
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # 验证服务 - 备用实例
  verify-service-2:
    build:
      context: .
      dockerfile: Dockerfile.verify
    container_name: pdf-qr-verify-2
    restart: unless-stopped
    ports:
      - "5006:5005"
    volumes:
      # 只读访问处理后的文件
      - ./processed:/app/processed:ro
      - ./data:/app/data:ro
      - ./logs/verify-2:/app/logs
    environment:
      - FLASK_ENV=production
      - PYTHONPATH=/app
      - SERVICE_TYPE=verify
      - INSTANCE_ID=verify-2
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5005/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - pdf-qr-network
    depends_on:
      - data-sync
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # 数据同步服务
  data-sync:
    build:
      context: .
      dockerfile: Dockerfile.datasync
    container_name: pdf-qr-datasync
    restart: unless-stopped
    volumes:
      - ./data:/app/data
      - ./processed:/app/processed
      - ./backup:/app/backup
      - ./logs/datasync:/app/logs
    environment:
      - PYTHONPATH=/app
      - BACKUP_INTERVAL=300  # 5分钟备份一次
    networks:
      - pdf-qr-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Nginx 负载均衡器
  nginx-lb:
    image: nginx:alpine
    container_name: pdf-qr-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
    networks:
      - pdf-qr-network
    depends_on:
      - verify-service-1
      - verify-service-2
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # 监控服务
  monitoring:
    build:
      context: .
      dockerfile: Dockerfile.monitoring
    container_name: pdf-qr-monitoring
    restart: unless-stopped
    ports:
      - "9090:9090"  # Prometheus
      - "3000:3000"  # Grafana
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - ./monitoring/grafana:/var/lib/grafana
      - ./logs/monitoring:/app/logs
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    networks:
      - pdf-qr-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

networks:
  pdf-qr-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  grafana-storage:
  prometheus-storage:
