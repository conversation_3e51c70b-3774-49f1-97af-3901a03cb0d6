# Nginx负载均衡配置
user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    'rt=$request_time uct="$upstream_connect_time" '
                    'uht="$upstream_header_time" urt="$upstream_response_time"';

    access_log /var/log/nginx/access.log main;

    # 基本配置
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 20M;

    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    # 验证服务上游配置
    upstream verify_backend {
        # 健康检查配置
        least_conn;  # 最少连接负载均衡
        
        # 主验证服务实例
        server verify-service-1:5005 weight=3 max_fails=3 fail_timeout=30s;
        
        # 备用验证服务实例
        server verify-service-2:5005 weight=1 max_fails=3 fail_timeout=30s backup;
        
        # 保持连接
        keepalive 32;
    }

    # 主应用上游配置
    upstream main_backend {
        server main-app:6065 weight=1 max_fails=3 fail_timeout=30s;
        keepalive 16;
    }

    # 限流配置
    limit_req_zone $binary_remote_addr zone=verify_limit:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=main_limit:10m rate=5r/s;

    # 验证服务虚拟主机 (ruxye.gaoliming.top)
    server {
        listen 80;
        server_name ruxye.gaoliming.top;
        
        # 重定向到HTTPS
        return 301 https://$server_name$request_uri;
    }

    server {
        listen 443 ssl http2;
        server_name ruxye.gaoliming.top;

        # SSL配置
        ssl_certificate /etc/nginx/ssl/ruxye.gaoliming.top.crt;
        ssl_certificate_key /etc/nginx/ssl/ruxye.gaoliming.top.key;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
        ssl_prefer_server_ciphers off;
        ssl_session_cache shared:SSL:10m;
        ssl_session_timeout 10m;

        # 安全头
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

        # 限流
        limit_req zone=verify_limit burst=20 nodelay;

        # 健康检查端点
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }

        # 验证服务代理
        location / {
            proxy_pass http://verify_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # 超时配置
            proxy_connect_timeout 5s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
            
            # 缓存配置（对于静态文件）
            location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
                proxy_pass http://verify_backend;
                proxy_cache_valid 200 1h;
                expires 1h;
                add_header Cache-Control "public, immutable";
            }
            
            # 错误处理
            proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
            proxy_next_upstream_tries 2;
            proxy_next_upstream_timeout 10s;
        }

        # 错误页面
        error_page 500 502 503 504 /50x.html;
        location = /50x.html {
            root /usr/share/nginx/html;
        }
    }

    # 主应用虚拟主机 (certificate.gaoliming.top)
    server {
        listen 80;
        server_name certificate.gaoliming.top;
        
        # 重定向到HTTPS
        return 301 https://$server_name$request_uri;
    }

    server {
        listen 443 ssl http2;
        server_name certificate.gaoliming.top;

        # SSL配置
        ssl_certificate /etc/nginx/ssl/certificate.gaoliming.top.crt;
        ssl_certificate_key /etc/nginx/ssl/certificate.gaoliming.top.key;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
        ssl_prefer_server_ciphers off;
        ssl_session_cache shared:SSL:10m;
        ssl_session_timeout 10m;

        # 安全头
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

        # 限流
        limit_req zone=main_limit burst=10 nodelay;

        # 主应用代理
        location / {
            proxy_pass http://main_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # 超时配置
            proxy_connect_timeout 5s;
            proxy_send_timeout 120s;  # 文件上传需要更长时间
            proxy_read_timeout 120s;
            
            # 文件上传配置
            client_max_body_size 20M;
        }

        # 错误页面
        error_page 500 502 503 504 /50x.html;
        location = /50x.html {
            root /usr/share/nginx/html;
        }
    }

    # 监控面板 (可选)
    server {
        listen 8080;
        server_name _;

        location /nginx_status {
            stub_status on;
            access_log off;
            allow **********/16;  # 只允许内网访问
            deny all;
        }
    }
}
