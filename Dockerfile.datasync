# 数据同步服务 Dockerfile
FROM python:3.10-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    rsync \
    curl \
    cron \
    && rm -rf /var/lib/apt/lists/*

# 复制数据同步服务的requirements
COPY requirements.datasync.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.datasync.txt

# 复制数据同步服务文件
COPY services/data_sync_service.py .
COPY utils/data_manager.py ./utils/
COPY utils/backup_manager.py ./utils/

# 创建必要的目录
RUN mkdir -p data processed backup logs

# 设置环境变量
ENV PYTHONPATH=/app
ENV SERVICE_TYPE=datasync

# 健康检查
HEALTHCHECK --interval=60s --timeout=10s --start-period=30s --retries=3 \
    CMD python -c "import os; exit(0 if os.path.exists('/app/data/url_mapping.json') else 1)"

# 创建非root用户
RUN useradd -m -u 1000 appuser && chown -R appuser:appuser /app
USER appuser

# 启动命令
CMD ["python", "data_sync_service.py"]
