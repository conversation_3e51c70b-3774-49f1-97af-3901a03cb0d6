# Prometheus配置文件
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  # Prometheus自身监控
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # 主应用服务监控
  - job_name: 'main-app'
    static_configs:
      - targets: ['main-app:6065']
    metrics_path: '/metrics'
    scrape_interval: 30s
    scrape_timeout: 10s

  # 验证服务监控
  - job_name: 'verify-service-1'
    static_configs:
      - targets: ['verify-service-1:5005']
    metrics_path: '/metrics'
    scrape_interval: 30s
    scrape_timeout: 10s

  - job_name: 'verify-service-2'
    static_configs:
      - targets: ['verify-service-2:5005']
    metrics_path: '/metrics'
    scrape_interval: 30s
    scrape_timeout: 10s

  # Docker容器监控
  - job_name: 'docker'
    static_configs:
      - targets: ['host.docker.internal:9323']
    scrape_interval: 30s

  # 系统监控
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['host.docker.internal:9100']
    scrape_interval: 30s
