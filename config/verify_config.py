import os
from datetime import timedelta

class VerifyConfig:
    """验证服务专用配置类"""
    
    # 基本配置
    SECRET_KEY = os.environ.get('VERIFY_SECRET_KEY') or 'verify-secret-key-change-in-production'
    
    # 文件访问配置（只读）
    PROCESSED_FOLDER = 'processed'
    
    # 数据存储配置（只读）
    DATA_FOLDER = 'data'
    URL_MAPPING_FILE = os.path.join(DATA_FOLDER, 'url_mapping.json')
    
    # 服务标识
    SERVICE_TYPE = os.environ.get('SERVICE_TYPE', 'verify')
    INSTANCE_ID = os.environ.get('INSTANCE_ID', 'verify-1')
    
    # 会话配置
    PERMANENT_SESSION_LIFETIME = timedelta(hours=24)
    
    # 缓存配置
    CACHE_TIMEOUT = 300  # 5分钟缓存
    
    # 安全配置
    MAX_REQUESTS_PER_MINUTE = 60
    
    @staticmethod
    def init_app(app):
        """初始化验证服务配置"""
        # 验证服务只需要确保数据目录存在（只读）
        if not os.path.exists(VerifyConfig.PROCESSED_FOLDER):
            raise Exception(f"处理文件目录不存在: {VerifyConfig.PROCESSED_FOLDER}")
        
        if not os.path.exists(VerifyConfig.DATA_FOLDER):
            raise Exception(f"数据目录不存在: {VerifyConfig.DATA_FOLDER}")

class VerifyDevelopmentConfig(VerifyConfig):
    """验证服务开发环境配置"""
    DEBUG = True

class VerifyProductionConfig(VerifyConfig):
    """验证服务生产环境配置"""
    DEBUG = False
    
    # 生产环境安全配置
    MAX_REQUESTS_PER_MINUTE = 30

# 验证服务配置字典
verify_config = {
    'development': VerifyDevelopmentConfig,
    'production': VerifyProductionConfig,
    'default': VerifyDevelopmentConfig
}
