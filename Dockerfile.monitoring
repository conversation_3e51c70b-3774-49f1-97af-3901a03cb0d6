# 监控服务 Dockerfile
FROM python:3.10-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    && rm -rf /var/lib/apt/lists/*

# 安装 Prometheus
RUN wget https://github.com/prometheus/prometheus/releases/download/v2.45.0/prometheus-2.45.0.linux-amd64.tar.gz && \
    tar xvfz prometheus-*.tar.gz && \
    mv prometheus-*/prometheus /usr/local/bin/ && \
    mv prometheus-*/promtool /usr/local/bin/ && \
    rm -rf prometheus-*

# 安装 Grafana
RUN wget https://dl.grafana.com/oss/release/grafana-10.0.0.linux-amd64.tar.gz && \
    tar -zxvf grafana-*.tar.gz && \
    mv grafana-*/ /opt/grafana && \
    rm grafana-*.tar.gz

# 复制监控服务的requirements
COPY requirements.monitoring.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.monitoring.txt

# 复制监控服务文件
COPY services/monitoring_service.py .
COPY monitoring/ ./monitoring/

# 创建必要的目录
RUN mkdir -p logs prometheus grafana

# 设置环境变量
ENV PYTHONPATH=/app
ENV SERVICE_TYPE=monitoring

# 暴露端口
EXPOSE 9090 3000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:9090/-/healthy && curl -f http://localhost:3000/api/health

# 创建非root用户
RUN useradd -m -u 1000 appuser && chown -R appuser:appuser /app /opt/grafana
USER appuser

# 启动命令
CMD ["python", "monitoring_service.py"]
