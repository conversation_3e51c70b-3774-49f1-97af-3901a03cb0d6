# PDF二维码系统 - 高可用性架构文档

## 概述

本文档描述了PDF二维码系统的高可用性架构设计，确保验证服务能够在主应用服务崩溃时独立运行，实现真正的服务隔离和容错能力。

## 架构设计

### 服务拓扑

```
┌─────────────────┐    ┌─────────────────┐
│   用户请求      │    │   管理员        │
└─────────┬───────┘    └─────────┬───────┘
          │                      │
          ▼                      ▼
┌─────────────────────────────────────────┐
│           Nginx 负载均衡器               │
│     (SSL终止 + 故障转移)                │
└─────────┬───────────────────────────────┘
          │
          ├─── 主应用服务 (6065)
          │    ├─── 文件上传处理
          │    ├─── 二维码生成
          │    └─── 图片合成
          │
          └─── 验证服务集群
               ├─── 验证服务1 (5005) [主]
               ├─── 验证服务2 (5006) [备]
               └─── 数据同步服务
                    ├─── 数据备份
                    ├─── 完整性检查
                    └─── 故障恢复

┌─────────────────────────────────────────┐
│              监控系统                    │
│  ├─── Prometheus (指标收集)             │
│  ├─── Grafana (可视化)                  │
│  └─── 自动恢复机制                      │
└─────────────────────────────────────────┘
```

### 核心特性

#### 1. 服务隔离
- **独立容器**: 每个服务运行在独立的Docker容器中
- **资源隔离**: CPU、内存、网络资源完全隔离
- **故障隔离**: 单个服务故障不影响其他服务

#### 2. 数据独立性
- **只读访问**: 验证服务对文件系统只读访问
- **数据同步**: 独立的数据同步服务确保数据一致性
- **备份机制**: 自动备份和恢复机制

#### 3. 高可用性
- **负载均衡**: Nginx实现智能负载均衡和故障转移
- **健康检查**: 多层次健康检查机制
- **自动恢复**: 服务故障自动重启和恢复

## 部署指南

### 1. 环境要求

```bash
# 系统要求
- Docker 20.10+
- Docker Compose 2.0+
- 至少 4GB RAM
- 至少 20GB 磁盘空间

# 网络要求
- 端口 80, 443 (Nginx)
- 端口 6065 (主应用)
- 端口 5005, 5006 (验证服务)
- 端口 9090, 3000 (监控)
```

### 2. 快速部署

```bash
# 1. 克隆项目
git clone <repository>
cd pdf-qr-system

# 2. 执行高可用性部署
chmod +x scripts/deploy_ha.sh
./scripts/deploy_ha.sh

# 3. 验证部署
docker-compose -f docker-compose.ha.yml ps
```

### 3. 配置SSL证书

```bash
# 将SSL证书放置到指定目录
mkdir -p nginx/ssl
cp your-cert.crt nginx/ssl/ruxye.gaoliming.top.crt
cp your-key.key nginx/ssl/ruxye.gaoliming.top.key
cp your-cert.crt nginx/ssl/certificate.gaoliming.top.crt
cp your-key.key nginx/ssl/certificate.gaoliming.top.key

# 重启Nginx
docker-compose -f docker-compose.ha.yml restart nginx-lb
```

## 测试验证

### 1. 独立性测试

验证验证服务在主应用崩溃时的独立运行能力：

```bash
# 运行独立性测试
python3 scripts/test_independence.py

# 测试内容：
# - 创建测试数据
# - 停止主应用服务
# - 持续测试验证服务5分钟
# - 分析成功率和响应时间
# - 恢复主应用服务
```

### 2. 故障转移测试

测试负载均衡和故障转移机制：

```bash
# 运行故障转移测试
python3 scripts/failover_test.py

# 测试场景：
# - 主验证服务故障
# - 负载均衡切换
# - 服务自动恢复
# - 并发访问测试
```

### 3. 性能测试

```bash
# 并发访问测试
ab -n 1000 -c 10 http://localhost/test_verification_id

# 预期结果：
# - 成功率 > 99%
# - 平均响应时间 < 100ms
# - 无服务中断
```

## 监控和运维

### 1. 服务监控

访问监控面板：
- **Prometheus**: http://localhost:9090
- **Grafana**: http://localhost:3000 (admin/admin123)

关键指标：
- 服务可用性
- 响应时间
- 错误率
- 资源使用率

### 2. 日志管理

```bash
# 查看所有服务日志
docker-compose -f docker-compose.ha.yml logs -f

# 查看特定服务日志
docker-compose -f docker-compose.ha.yml logs -f verify-service-1

# 日志文件位置
./logs/
├── main/           # 主应用日志
├── verify-1/       # 验证服务1日志
├── verify-2/       # 验证服务2日志
├── datasync/       # 数据同步日志
├── monitoring/     # 监控服务日志
└── nginx/          # Nginx日志
```

### 3. 备份管理

```bash
# 查看备份状态
docker exec pdf-qr-datasync ls -la /app/backup/

# 手动创建备份
docker exec pdf-qr-datasync python -c "
from data_sync_service import DataSyncService
service = DataSyncService()
service.create_backup()
"

# 恢复备份
cp backup/backup_YYYYMMDD_HHMMSS/url_mapping.json data/
```

## 故障处理

### 1. 常见故障场景

#### 验证服务故障
```bash
# 检查服务状态
docker ps | grep verify

# 重启故障服务
docker-compose -f docker-compose.ha.yml restart verify-service-1

# 查看错误日志
docker-compose -f docker-compose.ha.yml logs verify-service-1
```

#### 数据同步故障
```bash
# 检查数据完整性
docker exec pdf-qr-datasync python -c "
from data_sync_service import DataSyncService
service = DataSyncService()
print(service.verify_data_integrity())
"

# 重启数据同步服务
docker-compose -f docker-compose.ha.yml restart data-sync
```

#### 负载均衡器故障
```bash
# 检查Nginx配置
docker exec pdf-qr-nginx nginx -t

# 重新加载配置
docker exec pdf-qr-nginx nginx -s reload

# 重启负载均衡器
docker-compose -f docker-compose.ha.yml restart nginx-lb
```

### 2. 应急处理

#### 完全服务恢复
```bash
# 停止所有服务
docker-compose -f docker-compose.ha.yml down

# 清理容器和网络
docker system prune -f

# 重新部署
./scripts/deploy_ha.sh
```

#### 数据恢复
```bash
# 从备份恢复数据
cp backup/backup_LATEST/url_mapping.json data/
cp -r backup/backup_LATEST/processed/* processed/

# 重启相关服务
docker-compose -f docker-compose.ha.yml restart verify-service-1 verify-service-2
```

## 性能优化

### 1. 容器资源限制

在 `docker-compose.ha.yml` 中添加资源限制：

```yaml
services:
  verify-service-1:
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M
```

### 2. Nginx优化

```nginx
# 在 nginx.conf 中优化配置
worker_processes auto;
worker_connections 2048;
keepalive_timeout 65;
client_max_body_size 50M;
```

### 3. 数据库优化

如果使用数据库，考虑：
- 连接池配置
- 索引优化
- 查询缓存

## 安全考虑

### 1. 网络安全
- 使用内部网络隔离服务
- 只暴露必要的端口
- 配置防火墙规则

### 2. 访问控制
- 限制API访问频率
- 实施IP白名单
- 使用强密码策略

### 3. 数据安全
- 定期备份数据
- 加密敏感信息
- 审计访问日志

## 扩展性

### 1. 水平扩展
- 增加更多验证服务实例
- 配置数据库集群
- 使用外部负载均衡器

### 2. 垂直扩展
- 增加容器资源配额
- 优化应用性能
- 使用更强大的硬件

## 总结

本高可用性架构确保了：

1. **服务独立性**: 验证服务完全独立于主应用服务
2. **故障隔离**: 单点故障不影响整体系统
3. **自动恢复**: 故障自动检测和恢复
4. **数据一致性**: 可靠的数据同步和备份机制
5. **监控可视化**: 全面的监控和告警系统

通过这个架构，已下载的二维码图片的验证功能将不受主应用服务状态影响，真正实现了高可用性和容错能力。
